S3 Hierarchical Storage Test Results
========================================
Current working directory: /Users/<USER>/Documents/SE-TRANSITION-AGI-TECH/backend

1. Testing S3 hierarchical storage imports...
   ✅ Imports successful!

2. Testing multi-plant extraction with S3 storage...
   ✅ Multi-plant extraction completed!
   Success: True
   Plants processed: 2
   Summary: Processed 2 plants, 2 successful, 0 failed

3. S3 Storage Results:
   Plant 1: Belews Creek Steam Station
     S3 Success: True
     S3 URL: https://clem-transition-tech.s3.amazonaws.com/United States of America/ORG_UN_C88C2F_52646184/9b314ef2-253b-4d05-83d5-5ca9a643cfa1/plant#belews_creek_steam_station#1.json
     S3 Error: No error

   Plant 2: Marshall Steam Station
     S3 Success: True
     S3 URL: https://clem-transition-tech.s3.amazonaws.com/United States of America/ORG_UN_C88C2F_52646184/6671376a-e23b-41ad-88e7-e4de459324b2/plant#marshall_steam_station#2.json
     S3 Error: No error

✅ ALL TESTS COMPLETED!
