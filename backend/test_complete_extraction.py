#!/usr/bin/env python3
"""
Test complete plant extraction with proper SK-based file naming
"""

import sys
import os

# Write output to file
with open('test_complete_results.txt', 'w') as f:
    f.write("Complete Plant Extraction Test Results\n")
    f.write("=" * 45 + "\n")
    f.write(f"Current working directory: {os.getcwd()}\n\n")
    
    try:
        f.write("1. Testing complete plant extraction imports...\n")
        from src.agent.graph import run_complete_plant_extraction
        f.write("   ✅ Imports successful!\n\n")
        
        f.write("2. Testing complete plant extraction with S3 storage...\n")
        
        # Test data
        plant_name = "Belews Creek Steam Station"
        plant_uuid = "test-plant-uuid-123"
        org_uid = "ORG_UN_C88C2F_52646184"
        org_name = "Duke Energy Corporation"
        country = "United States of America"
        session_id = "test-complete-session"
        
        # Mock state
        state = {
            "messages": [{"role": "user", "content": plant_name}],
            "session_id": session_id
        }
        
        result = run_complete_plant_extraction(
            plant_name=plant_name,
            plant_uuid=plant_uuid,
            org_uid=org_uid,
            org_name=org_name,
            country=country,
            session_id=session_id,
            state=state
        )
        
        f.write("   ✅ Complete plant extraction completed!\n")
        f.write(f"   Success: {result.get('success', 'unknown')}\n")
        f.write(f"   Plant Name: {result.get('plant_name', 'unknown')}\n")
        f.write(f"   Plant UUID: {result.get('plant_uuid', 'unknown')}\n\n")
        
        if result.get('s3_urls'):
            f.write("3. S3 Storage Results with SK-based naming:\n")
            s3_urls = result['s3_urls']
            f.write(f"   Organization URL: {s3_urls.get('organization', 'No URL')}\n")
            f.write(f"   Plant URL: {s3_urls.get('plant', 'No URL')}\n")
            f.write(f"   Unit URLs: {s3_urls.get('units', [])}\n")
            f.write(f"   Image URLs: {len(s3_urls.get('images', []))} images\n\n")
        
        if result.get('extraction_summary'):
            f.write("4. Extraction Summary:\n")
            summary = result['extraction_summary']
            f.write(f"   Organization Complete: {summary.get('organization_complete', False)}\n")
            f.write(f"   Plant Complete: {summary.get('plant_complete', False)}\n")
            f.write(f"   Unit Complete: {summary.get('unit_complete', False)}\n")
            f.write(f"   Images Extracted: {summary.get('images_extracted', 0)}\n")
        
        f.write("\n✅ ALL TESTS COMPLETED!\n")
        
    except Exception as e:
        f.write(f"❌ Error: {e}\n")
        import traceback
        f.write(traceback.format_exc())

print("Complete extraction test completed. Check test_complete_results.txt for results.")
