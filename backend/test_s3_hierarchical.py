#!/usr/bin/env python3
"""
Test S3 hierarchical storage functionality
"""

import sys
import os

# Write output to file
with open('test_s3_results.txt', 'w') as f:
    f.write("S3 Hierarchical Storage Test Results\n")
    f.write("=" * 40 + "\n")
    f.write(f"Current working directory: {os.getcwd()}\n\n")
    
    try:
        f.write("1. Testing S3 hierarchical storage imports...\n")
        from src.agent.json_s3_storage import store_hierarchical_plant_data
        from src.agent.multi_plant_extraction import run_multi_plant_extraction_for_org
        f.write("   ✅ Imports successful!\n\n")
        
        f.write("2. Testing multi-plant extraction with S3 storage...\n")
        result = run_multi_plant_extraction_for_org('ORG_UN_C88C2F_52646184', 'test-s3-session')
        f.write("   ✅ Multi-plant extraction completed!\n")
        f.write(f"   Success: {result.get('success', 'unknown')}\n")
        f.write(f"   Plants processed: {result.get('plants_processed', 0)}\n")
        f.write(f"   Summary: {result.get('summary', 'No summary')}\n\n")
        
        if result.get('results'):
            f.write("3. S3 Storage Results:\n")
            for i, plant_result in enumerate(result['results'], 1):
                plant_name = plant_result.get('plant_name', 'Unknown')
                s3_storage = plant_result.get('s3_storage', {})
                f.write(f"   Plant {i}: {plant_name}\n")
                f.write(f"     S3 Success: {s3_storage.get('success', False)}\n")
                f.write(f"     S3 URL: {s3_storage.get('url', 'No URL')}\n")
                f.write(f"     S3 Error: {s3_storage.get('error', 'No error')}\n\n")
        
        f.write("✅ ALL TESTS COMPLETED!\n")
        
    except Exception as e:
        f.write(f"❌ Error: {e}\n")
        import traceback
        f.write(traceback.format_exc())

print("S3 test completed. Check test_s3_results.txt for results.")
