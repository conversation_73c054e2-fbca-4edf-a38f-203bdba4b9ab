#!/usr/bin/env python3

import sys
import os

# Write output to file
with open('test_output.txt', 'w') as f:
    f.write("Multi-Plant Import Test Results\n")
    f.write("=" * 40 + "\n")
    f.write(f"Current working directory: {os.getcwd()}\n")
    f.write(f"Python path: {sys.path[:3]}\n\n")
    
    try:
        f.write("1. Testing import from src.agent.multi_plant_extraction...\n")
        from src.agent.multi_plant_extraction import run_multi_plant_extraction_for_org
        f.write("   ✅ Import successful!\n\n")
        
        f.write("2. Testing function call...\n")
        result = run_multi_plant_extraction_for_org('ORG_UN_C88C2F_52646184', 'test-session')
        f.write("   ✅ Function call successful!\n")
        f.write(f"   Success: {result.get('success', 'unknown')}\n")
        f.write(f"   Plants processed: {result.get('plants_processed', 0)}\n")
        f.write(f"   Summary: {result.get('summary', 'No summary')}\n\n")
        
        f.write("✅ ALL TESTS PASSED!\n")
        
    except Exception as e:
        f.write(f"❌ Error: {e}\n")
        import traceback
        f.write(traceback.format_exc())

print("Test completed. Check test_output.txt for results.")
