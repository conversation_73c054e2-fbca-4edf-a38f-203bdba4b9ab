Multi-Plant Import Test Results
========================================
Current working directory: /Users/<USER>/Documents/SE-TRANSITION-AGI-TECH/backend
Python path: ['/Users/<USER>/Documents/SE-TRANSITION-AGI-TECH/backend', '/Users/<USER>/miniconda3/lib/python312.zip', '/Users/<USER>/miniconda3/lib/python3.12']

1. Testing import from src.agent.multi_plant_extraction...
   ✅ Import successful!

2. Testing function call...
   ✅ Function call successful!
   Success: True
   Plants processed: 2
   Summary: Processed 2 plants, 2 successful, 0 failed

✅ ALL TESTS PASSED!
