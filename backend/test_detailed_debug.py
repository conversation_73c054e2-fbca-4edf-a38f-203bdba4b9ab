#!/usr/bin/env python3
"""
Detailed debug test for multi-plant extraction
"""

import sys
import os

# Write output to file
with open('test_detailed_debug_results.txt', 'w') as f:
    f.write("Detailed Debug Multi-Plant Test Results\n")
    f.write("=" * 45 + "\n")
    f.write(f"Current working directory: {os.getcwd()}\n\n")
    
    try:
        f.write("1. Testing individual helper functions...\n")
        
        # Test extract_organization_data_once
        f.write("   Testing extract_organization_data_once...\n")
        from src.agent.multi_plant_extraction import extract_organization_data_once
        
        sample_plant = {
            'plant_name': 'Test Plant',
            'plant_uuid': 'test-uuid',
            'country': 'United States of America',
            'org_name': 'Duke Energy Corporation'
        }
        
        org_s3_url = extract_organization_data_once(
            'ORG_UN_C88C2F_52646184', 
            'Duke Energy Corporation', 
            sample_plant, 
            'test-debug-org'
        )
        f.write(f"   Organization S3 URL: {org_s3_url}\n")
        
        # Test process_single_plant_simple
        f.write("   Testing process_single_plant_simple...\n")
        from src.agent.multi_plant_extraction import process_single_plant_simple
        
        plant_result = process_single_plant_simple(
            plant=sample_plant,
            org_uid='ORG_UN_C88C2F_52646184',
            org_name='Duke Energy Corporation',
            session_id='test-debug-plant'
        )
        
        f.write(f"   Plant processing success: {plant_result.get('success', False)}\n")
        f.write(f"   Plant processing error: {plant_result.get('error', 'No error')}\n")
        
        if plant_result.get('s3_urls'):
            s3_urls = plant_result['s3_urls']
            f.write(f"   Plant S3 URL: {s3_urls.get('plant', 'No URL')}\n")
            f.write(f"   Unit S3 URLs: {len(s3_urls.get('units', []))} units\n")
            f.write(f"   Image URLs: {len(s3_urls.get('images', []))} images\n")
        
        f.write("\n2. Testing full multi-plant extraction...\n")
        from src.agent.multi_plant_extraction import run_multi_plant_extraction_for_org
        
        result = run_multi_plant_extraction_for_org('ORG_UN_C88C2F_52646184', 'test-debug-full')
        
        f.write(f"   Overall success: {result.get('success', 'unknown')}\n")
        f.write(f"   Plants processed: {result.get('plants_processed', 0)}\n")
        f.write(f"   Successful extractions: {result.get('successful_extractions', 0)}\n")
        f.write(f"   Failed extractions: {result.get('failed_extractions', 0)}\n")
        f.write(f"   Organization S3 URL: {result.get('org_s3_url', 'No URL')}\n")
        
        if result.get('results'):
            f.write("\n3. Individual plant results:\n")
            for i, plant_result in enumerate(result['results'], 1):
                f.write(f"   Plant {i}: {plant_result.get('plant_name', 'Unknown')}\n")
                f.write(f"     Success: {plant_result.get('success', False)}\n")
                f.write(f"     Error: {plant_result.get('error', 'No error')}\n")
                
                if plant_result.get('s3_urls'):
                    s3_urls = plant_result['s3_urls']
                    f.write(f"     Plant S3: {s3_urls.get('plant', 'No URL')}\n")
                    f.write(f"     Units: {len(s3_urls.get('units', []))} unit files\n")
                    f.write(f"     Images: {len(s3_urls.get('images', []))} images\n")
                f.write("\n")
        
        f.write("✅ DETAILED DEBUG COMPLETED!\n")
        
    except Exception as e:
        f.write(f"❌ Error: {e}\n")
        import traceback
        f.write(traceback.format_exc())

print("Detailed debug test completed. Check test_detailed_debug_results.txt for results.")
