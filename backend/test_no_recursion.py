#!/usr/bin/env python3
"""
Test the simplified multi-plant extraction to ensure no recursion
"""

import sys
import os

# Write output to file
with open('test_no_recursion_results.txt', 'w') as f:
    f.write("No Recursion Multi-Plant Test Results\n")
    f.write("=" * 40 + "\n")
    f.write(f"Current working directory: {os.getcwd()}\n\n")
    
    try:
        f.write("1. Testing simplified multi-plant extraction...\n")
        from src.agent.multi_plant_extraction import run_multi_plant_extraction_for_org
        f.write("   ✅ Import successful!\n\n")
        
        f.write("2. Running multi-plant extraction (should avoid recursion)...\n")
        
        # This should use the NEW simplified approach
        result = run_multi_plant_extraction_for_org('ORG_UN_C88C2F_52646184', 'test-no-recursion')
        
        f.write("   ✅ Multi-plant extraction completed without recursion!\n")
        f.write(f"   Success: {result.get('success', 'unknown')}\n")
        f.write(f"   Plants processed: {result.get('plants_processed', 0)}\n")
        f.write(f"   Successful extractions: {result.get('successful_extractions', 0)}\n")
        f.write(f"   Failed extractions: {result.get('failed_extractions', 0)}\n")
        f.write(f"   Summary: {result.get('summary', 'No summary')}\n\n")
        
        f.write("3. Testing organization extraction (should happen only once)...\n")
        if result.get('results'):
            f.write(f"   Total plants processed: {len(result['results'])}\n")
            for i, plant_result in enumerate(result['results'], 1):
                plant_name = plant_result.get('plant_name', 'Unknown')
                success = plant_result.get('success', False)
                f.write(f"   Plant {i}: {plant_name} - Success: {success}\n")
                
                if plant_result.get('s3_urls'):
                    s3_urls = plant_result['s3_urls']
                    f.write(f"     Plant S3: {s3_urls.get('plant', 'No URL')}\n")
                    f.write(f"     Units S3: {len(s3_urls.get('units', []))} unit files\n")
                    f.write(f"     Images: {len(s3_urls.get('images', []))} images\n")
        
        f.write("\n4. Organization extraction check:\n")
        org_s3_url = result.get('org_s3_url', '')
        if org_s3_url:
            f.write(f"   ✅ Organization data extracted ONCE: {org_s3_url}\n")
        else:
            f.write("   ⚠️ No organization S3 URL found\n")
        
        f.write("\n✅ ALL TESTS COMPLETED - NO RECURSION DETECTED!\n")
        
    except Exception as e:
        f.write(f"❌ Error: {e}\n")
        import traceback
        f.write(traceback.format_exc())

print("No recursion test completed. Check test_no_recursion_results.txt for results.")
