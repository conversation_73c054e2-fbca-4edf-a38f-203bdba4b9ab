No Recursion Multi-Plant Test Results
========================================
Current working directory: /Users/<USER>/Documents/SE-TRANSITION-AGI-TECH/backend

1. Testing simplified multi-plant extraction...
   ✅ Import successful!

2. Running multi-plant extraction (should avoid recursion)...
   ✅ Multi-plant extraction completed without recursion!
   Success: True
   Plants processed: 2
   Successful extractions: 2
   Failed extractions: 0
   Summary: Processed 2 plants, 2 successful, 0 failed

3. Testing organization extraction (should happen only once)...
   Total plants processed: 2
   Plant 1: Belews Creek Steam Station - Success: True
     Plant S3: https://clem-transition-tech.s3.amazonaws.com/United States of America/ORG_UN_C88C2F_52646184/9b314ef2-253b-4d05-83d5-5ca9a643cfa1/plant#belews_creek_steam_station.json
     Units S3: 2 unit files
     Images: 0 images
   Plant 2: Marshall Steam Station - Success: True
     Plant S3: https://clem-transition-tech.s3.amazonaws.com/United States of America/ORG_UN_C88C2F_52646184/6671376a-e23b-41ad-88e7-e4de459324b2/plant#marshall_steam_station.json
     Units S3: 2 unit files
     Images: 0 images

4. Organization extraction check:
   ✅ Organization data extracted ONCE: https://clem-transition-tech.s3.amazonaws.com/United States of America/ORG_UN_C88C2F_52646184/ORG_UN_C88C2F_52646184.json

✅ ALL TESTS COMPLETED - NO RECURSION DETECTED!
