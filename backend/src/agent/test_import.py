#!/usr/bin/env python3

import sys
import os

# Set working directory and path
os.chdir('/Users/<USER>/Documents/SE-TRANSITION-AGI-TECH/backend/src/agent')
sys.path.insert(0, '.')

# Write results to file
with open('test_results.txt', 'w') as f:
    f.write("Multi-Plant Extraction Test Results\n")
    f.write("=" * 40 + "\n\n")
    
    try:
        f.write("1. Testing import...\n")
        from multi_plant_extraction import run_multi_plant_extraction_for_org
        f.write("   ✅ Import successful\n\n")
        
        f.write("2. Testing function call...\n")
        result = run_multi_plant_extraction_for_org('ORG_UN_C88C2F_52646184', 'test-session')
        f.write("   ✅ Function call successful\n")
        f.write(f"   Success: {result.get('success', 'unknown')}\n")
        f.write(f"   Plants processed: {result.get('plants_processed', 0)}\n")
        f.write(f"   Summary: {result.get('summary', 'No summary')}\n\n")
        
        f.write("3. Testing graph function...\n")
        from graph import run_multi_plant_extraction
        test_state = {
            'session_id': 'test-session-123',
            'org_uid': 'ORG_UN_C88C2F_52646184'
        }
        graph_result = run_multi_plant_extraction(test_state)
        f.write("   ✅ Graph function call successful\n")
        f.write(f"   Result type: {type(graph_result)}\n")
        if isinstance(graph_result, dict):
            f.write(f"   Keys: {list(graph_result.keys())}\n")
            if 'multi_plant_error' in graph_result:
                f.write(f"   Error: {graph_result['multi_plant_error']}\n")
        
        f.write("\n✅ ALL TESTS PASSED!\n")
        
    except Exception as e:
        f.write(f"❌ Error: {e}\n")
        import traceback
        f.write(traceback.format_exc())

print("Test completed. Check test_results.txt for results.")
