#!/usr/bin/env python3
"""
Test script for multi-plant extraction functionality
"""

import sys
import os

# Add current directory to Python path
sys.path.insert(0, '.')

def test_multi_plant_extraction():
    """Test the multi-plant extraction functionality"""
    
    print("🧪 Testing Multi-Plant Extraction")
    print("=" * 50)
    
    try:
        # Test import
        print("1. Testing import...")
        from multi_plant_extraction import run_multi_plant_extraction_for_org
        print("   ✅ Import successful")
        
        # Test function call with test org_uid
        print("\n2. Testing function call...")
        org_uid = 'ORG_UN_C88C2F_52646184'  # Duke Energy from logs
        session_id = 'test-session-123'
        
        print(f"   Organization UID: {org_uid}")
        print(f"   Session ID: {session_id}")
        
        result = run_multi_plant_extraction_for_org(org_uid, session_id)
        print("   ✅ Function call successful")
        
        # Display results
        print("\n3. Results:")
        print(f"   Success: {result.get('success', 'unknown')}")
        print(f"   Plants processed: {result.get('plants_processed', 0)}")
        print(f"   Successful extractions: {result.get('successful_extractions', 0)}")
        print(f"   Summary: {result.get('summary', 'No summary')}")
        
        if result.get('results'):
            print(f"\n4. Plant Details:")
            for i, plant_result in enumerate(result['results'], 1):
                plant_name = plant_result.get('plant_name', 'Unknown')
                status = plant_result.get('extraction_status', 'unknown')
                print(f"   {i}. {plant_name} - Status: {status}")
        
        print("\n" + "=" * 50)
        print("🎉 Multi-plant extraction test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"   ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Runtime error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_database_connection():
    """Test database connection and plant data"""
    
    print("\n🗄️ Testing Database Connection")
    print("=" * 50)
    
    try:
        import sqlite3
        
        # Check if database exists
        db_path = 'powerplant_registry.db'
        if not os.path.exists(db_path):
            print(f"   ❌ Database file not found: {db_path}")
            return False
        
        print(f"   ✅ Database file found: {db_path}")
        
        # Connect and query
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get plant count
        cursor.execute('SELECT COUNT(*) FROM power_plants_registry')
        total_plants = cursor.fetchone()[0]
        print(f"   📊 Total plants in database: {total_plants}")
        
        # Get Duke Energy plants
        cursor.execute('''
            SELECT plant_name, org_uid, plant_uuid 
            FROM power_plants_registry 
            WHERE org_uid = ?
        ''', ('ORG_UN_C88C2F_52646184',))
        
        duke_plants = cursor.fetchall()
        print(f"   📊 Duke Energy plants: {len(duke_plants)}")
        
        for i, (plant_name, org_uid, plant_uuid) in enumerate(duke_plants, 1):
            print(f"      {i}. {plant_name} (UUID: {plant_uuid[:8]}...)")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"   ❌ Database error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Multi-Plant Extraction Test Suite")
    print("=" * 60)
    
    # Test database first
    db_success = test_database_connection()
    
    # Test multi-plant extraction
    extraction_success = test_multi_plant_extraction()
    
    print("\n" + "=" * 60)
    print("📋 Test Summary:")
    print(f"   Database Connection: {'✅ PASS' if db_success else '❌ FAIL'}")
    print(f"   Multi-Plant Extraction: {'✅ PASS' if extraction_success else '❌ FAIL'}")
    
    if db_success and extraction_success:
        print("\n🎉 All tests passed! Multi-plant extraction is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the errors above.")
        sys.exit(1)
