#!/usr/bin/env python3

import sys
import os

# Set working directory and path
os.chdir('/Users/<USER>/Documents/SE-TRANSITION-AGI-TECH/backend/src/agent')
sys.path.insert(0, '.')

print("🧪 Fresh Multi-Plant Extraction Test")
print("=" * 40)

try:
    print("1. Testing import...")
    from multi_plant_extraction import run_multi_plant_extraction_for_org
    print("   ✅ Import successful")
    
    print("\n2. Testing function call...")
    result = run_multi_plant_extraction_for_org('ORG_UN_C88C2F_52646184', 'test-session')
    print("   ✅ Function call successful")
    print(f"   Success: {result.get('success', 'unknown')}")
    print(f"   Plants processed: {result.get('plants_processed', 0)}")
    print(f"   Summary: {result.get('summary', 'No summary')}")
    
    print("\n✅ ALL TESTS PASSED!")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
