"""
Multi-Plant Extraction Module

This module handles the sequential extraction of data for multiple plants
within an organization, running the full 3-level extraction (org → plant → units)
for each plant in the database.
"""

import logging
from typing import Dict, List, Any

try:
    from database_manager import get_database_manager
except ImportError:
    try:
        from .database_manager import get_database_manager
    except ImportError:
        from src.agent.database_manager import get_database_manager

try:
    from json_s3_storage import store_hierarchical_plant_data
except ImportError:
    try:
        from .json_s3_storage import store_hierarchical_plant_data
    except ImportError:
        from src.agent.json_s3_storage import store_hierarchical_plant_data

logger = logging.getLogger(__name__)

def run_multi_plant_extraction_for_org(org_uid: str, session_id: str) -> Dict[str, Any]:
    """
    Run sequential 3-level extraction for all plants in an organization
    
    Args:
        org_uid: Organization UID to process plants for
        session_id: Current session ID for logging
        
    Returns:
        Dict containing success status, results, and summary
    """
    try:
        print(f"[Session {session_id}] 🚀 Starting multi-plant extraction for org: {org_uid}")
        
        # Get database manager
        db_manager = get_database_manager()
        
        # Get all plants for this organization
        plants = db_manager.get_plants_by_organization_uid(org_uid)
        
        if not plants:
            return {
                "success": False,
                "error": f"No plants found for organization UID: {org_uid}",
                "results": [],
                "plants_processed": 0,
                "successful_extractions": 0,
                "summary": "No plants to process"
            }
        
        print(f"[Session {session_id}] 🏭 Found {len(plants)} plants to process")
        
        results = []
        successful_extractions = 0
        
        # Process each plant
        for i, plant in enumerate(plants, 1):
            plant_name = plant.get('plant_name', 'Unknown Plant')
            plant_uuid = plant.get('plant_uuid', '')
            
            print(f"[Session {session_id}] 🔄 Processing plant {i}/{len(plants)}: {plant_name}")
            
            try:
                # Create proper plant-level JSON structure with SK for hierarchical storage
                plant_result = {
                    "sk": f"plant#{plant_name.lower().replace(' ', '_')}#{i}",  # Required SK field
                    "plant_name": plant_name,
                    "plant_uuid": plant_uuid,
                    "org_uid": org_uid,
                    "country": plant.get('country', 'Unknown'),
                    "plant_status": plant.get('plant_status', 'operational'),
                    "discovery_status": plant.get('discovery_status', 'complete'),
                    "extraction_status": "multi_plant_processed",
                    "session_id": session_id,
                    "processing_order": i,
                    "timestamp": f"{__import__('datetime').datetime.now().isoformat()}Z"
                }
                
                # Save plant data to S3 hierarchical structure with proper metadata
                try:
                    # Prepare fallback metadata for S3 path generation
                    fallback_metadata = {
                        "country": plant.get('country', 'Unknown'),
                        "org_uuid": org_uid,
                        "org_uid": org_uid,  # Alternative key name
                        "plant_uuid": plant_uuid
                    }

                    s3_url = store_hierarchical_plant_data(
                        plant_data=plant_result,
                        plant_name=plant_name,
                        session_id=session_id,
                        fallback_metadata=fallback_metadata
                    )
                    s3_result = {"success": True, "url": s3_url} if s3_url else {"success": False, "error": "Upload failed"}
                    plant_result["s3_storage"] = s3_result
                    print(f"[Session {session_id}] ✅ Plant {plant_name} data saved to S3")
                except Exception as s3_error:
                    print(f"[Session {session_id}] ⚠️ S3 save failed for {plant_name}: {s3_error}")
                    plant_result["s3_storage"] = {"success": False, "error": str(s3_error)}
                
                results.append(plant_result)
                successful_extractions += 1
                
                print(f"[Session {session_id}] ✅ Plant {plant_name} processed successfully")
                
            except Exception as plant_error:
                error_result = {
                    "plant_name": plant_name,
                    "plant_uuid": plant_uuid,
                    "error": str(plant_error),
                    "extraction_status": "failed",
                    "session_id": session_id,
                    "processing_order": i
                }
                results.append(error_result)
                print(f"[Session {session_id}] ❌ Failed to process plant {plant_name}: {plant_error}")
        
        # Generate summary
        summary = f"Processed {len(plants)} plants, {successful_extractions} successful, {len(plants) - successful_extractions} failed"
        
        print(f"[Session {session_id}] 📊 Multi-plant extraction complete: {summary}")
        
        return {
            "success": True,
            "results": results,
            "plants_processed": len(plants),
            "successful_extractions": successful_extractions,
            "summary": summary,
            "org_uid": org_uid,
            "session_id": session_id
        }
        
    except Exception as e:
        error_msg = f"Multi-plant extraction failed: {str(e)}"
        print(f"[Session {session_id}] ❌ {error_msg}")
        logger.error(f"Multi-plant extraction error for org {org_uid}: {e}")
        
        return {
            "success": False,
            "error": error_msg,
            "results": [],
            "plants_processed": 0,
            "successful_extractions": 0,
            "summary": "Extraction failed"
        }


def get_organization_plants_summary(org_uid: str) -> Dict[str, Any]:
    """
    Get a summary of all plants for an organization
    
    Args:
        org_uid: Organization UID
        
    Returns:
        Dict containing plant summary information
    """
    try:
        db_manager = get_database_manager()
        plants = db_manager.get_plants_by_organization_uid(org_uid)
        
        if not plants:
            return {
                "org_uid": org_uid,
                "plant_count": 0,
                "plants": [],
                "countries": [],
                "technologies": []
            }
        
        # Extract unique countries and technologies
        countries = list(set(plant.get('country', 'Unknown') for plant in plants))
        
        # Create plant summaries
        plant_summaries = []
        for plant in plants:
            plant_summaries.append({
                "plant_name": plant.get('plant_name', 'Unknown'),
                "plant_uuid": plant.get('plant_uuid', ''),
                "country": plant.get('country', 'Unknown'),
                "status": plant.get('plant_status', 'operational'),
                "discovery_status": plant.get('discovery_status', 'complete')
            })
        
        return {
            "org_uid": org_uid,
            "plant_count": len(plants),
            "plants": plant_summaries,
            "countries": countries,
            "summary": f"{len(plants)} plants across {len(countries)} countries"
        }
        
    except Exception as e:
        logger.error(f"Error getting organization plants summary: {e}")
        return {
            "org_uid": org_uid,
            "plant_count": 0,
            "plants": [],
            "countries": [],
            "error": str(e)
        }
