#!/usr/bin/env python3
"""
Test the multi-plant extraction import from the backend directory
"""

import sys
import os

print("🧪 Testing Multi-Plant Import from Backend Directory")
print("=" * 50)
print(f"Current working directory: {os.getcwd()}")
print(f"Python path: {sys.path[:3]}")

try:
    print("\n1. Testing import from src.agent.multi_plant_extraction...")
    from src.agent.multi_plant_extraction import run_multi_plant_extraction_for_org
    print("   ✅ Import successful!")
    
    print("\n2. Testing function call...")
    result = run_multi_plant_extraction_for_org('ORG_UN_C88C2F_52646184', 'test-session')
    print("   ✅ Function call successful!")
    print(f"   Success: {result.get('success', 'unknown')}")
    print(f"   Plants processed: {result.get('plants_processed', 0)}")
    print(f"   Successful extractions: {result.get('successful_extractions', 0)}")
    print(f"   Summary: {result.get('summary', 'No summary')}")
    
    if result.get('results'):
        print(f"\n3. Plant Details:")
        for i, plant_result in enumerate(result['results'], 1):
            plant_name = plant_result.get('plant_name', 'Unknown')
            status = plant_result.get('extraction_status', 'unknown')
            print(f"   {i}. {plant_name} - Status: {status}")
    
    print("\n✅ ALL TESTS PASSED! Multi-plant extraction is working correctly.")
    
except ImportError as e:
    print(f"   ❌ Import error: {e}")
    print("   Available modules in src/agent/:")
    try:
        import os
        agent_files = [f for f in os.listdir('src/agent') if f.endswith('.py')]
        for f in agent_files[:10]:  # Show first 10 files
            print(f"     - {f}")
    except:
        print("     Could not list files")
        
except Exception as e:
    print(f"   ❌ Runtime error: {e}")
    import traceback
    traceback.print_exc()
